import React, { useState } from 'react';
import { Layout, Menu, Typo<PERSON>, Avatar, Dropdown, Button, ConfigProvider } from 'antd';
import type { MenuProps } from 'antd';
import {
  UserOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  BookOutlined, // 学习中心图标
  SettingOutlined, // 用户菜单需要
} from '@ant-design/icons';
import { useAuthStore } from '../utils/authStore';
import { useNavigate, useLocation } from 'react-router-dom';
import { logout } from '../services/auth';

const { Sider, Content } = Layout;
const { Title } = Typography;

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const { user, logout: clearAuth } = useAuthStore();
  const navigate = useNavigate();
  const location = useLocation();

  const handleLogout = async () => {
    try {
      await logout();
      clearAuth();
      navigate('/login');
    } catch (error) {
      console.error('Logout error:', error);
      // 即使登出接口失败，也清除本地状态
      clearAuth();
      navigate('/login');
    }
  };

  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人信息',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  const sidebarMenuItems: MenuProps['items'] = [
    {
      key: '/dashboard',
      icon: <BookOutlined style={{
        fontSize: collapsed ? '24px' : '18px', // 收起时图标变大，与logo一样大
      }} />,
      label: '学习中心',
      onClick: () => navigate('/dashboard'),
    },
  ];

  return (
    <ConfigProvider
      theme={{
        components: {
          Menu: {
            itemColor: '#000000', // 菜单项文字颜色为黑色
            itemSelectedBg: '#f5f5f5', // 选中项背景色为灰色
            itemSelectedColor: '#000000', // 选中项文字颜色为黑色
            itemHoverBg: '#f5f5f5', // 悬停背景色为灰色
            itemHoverColor: '#000000', // 悬停文字颜色为黑色
            iconSize: collapsed ? 24 : 18, // 根据收起状态动态调整图标大小
          },
        },
      }}
    >
      <Layout style={{ minHeight: '100vh' }}>
        {/* 左侧边栏 */}
        <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        style={{
          background: '#fff',
          boxShadow: '2px 0 8px rgba(0,0,0,0.1)',
          zIndex: 1,
          display: 'flex',
          flexDirection: 'column',
          height: '100vh',  // 保持全屏高度
          position: 'relative', // 改回相对定位，让它参与正常布局流
        }}
        width={240}
        collapsedWidth={80}
      >
        {/* Logo区域 */}
        <div
          style={{
            height: 64,
            display: 'flex',
            alignItems: 'center',
            justifyContent: collapsed ? 'center' : 'space-between',
            padding: collapsed ? '0' : '0 24px',
            borderBottom: '1px solid #f0f0f0',
            flexShrink: 0,
            cursor: collapsed ? 'pointer' : 'default',
          }}
          onClick={collapsed ? () => setCollapsed(false) : undefined}
        >
          <div style={{
            display: 'flex',
            alignItems: 'center',
            flex: collapsed ? 'none' : 1,
            justifyContent: collapsed ? 'center' : 'flex-start'
          }}>
            {!collapsed ? (
              <>
                <img
                  src="/logo.png"
                  alt="锤磨AI"
                  style={{
                    height: 36,
                    width: 36,
                    objectFit: 'contain'
                  }}
                  onError={(e) => {
                    // 如果logo图片加载失败，显示文字logo
                    e.currentTarget.style.display = 'none';
                    const nextElement = e.currentTarget.nextElementSibling as HTMLElement;
                    if (nextElement) {
                      nextElement.style.display = 'block';
                    }
                  }}
                />
                <Title level={4} style={{ margin: 10, color: '#000000' }}>
                  锤磨AI
                </Title>
              </>
            ) : (
              <div style={{ position: 'relative' }}>
                <img
                  src="/logo.png"
                  alt="锤磨AI"
                  style={{
                    height: 40,
                    width: 40,
                    objectFit: 'contain',
                    transition: 'transform 0.2s ease',
                  }}
                  onError={(e) => {
                    // 如果logo图片加载失败，显示文字logo
                    e.currentTarget.style.display = 'none';
                    const nextElement = e.currentTarget.nextElementSibling as HTMLElement;
                    if (nextElement) {
                      nextElement.style.display = 'flex';
                    }
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'scale(1.1)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'scale(1)';
                  }}
                />
              </div>
            )}
          </div>

          {/* 折叠按钮 - 只在展开状态显示 */}
          {!collapsed && (
            <Button
              type="text"
              icon={<MenuFoldOutlined />}
              onClick={() => setCollapsed(true)}
              style={{
                fontSize: '16px',
                width: 32,
                height: 32,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            />
          )}
        </div>

        {/* 菜单区域 */}
        <div style={{
          flex: 1,
          overflow: 'auto',
          paddingBottom: collapsed ? '80px' : '100px', // 为底部用户信息区域留出空间
        }}>
          <style>
            {`
              /* 收起状态下菜单项图标居中对齐 */
              .ant-layout-sider-collapsed .ant-menu-item {
                padding-left: 0 !important;
                padding-right: 0 !important;
                text-align: center;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
              }

              .ant-layout-sider-collapsed .ant-menu-item .ant-menu-item-icon {
                margin-inline-end: 0 !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                width: 100% !important;
              }

              .ant-layout-sider-collapsed .ant-menu-item .ant-menu-title-content {
                display: none !important;
              }

              /* 确保图标在垂直方向也居中 */
              .ant-layout-sider-collapsed .ant-menu-item-icon svg {
                display: block !important;
                margin: 0 auto !important;
              }
            `}
          </style>
          <Menu
            mode="inline"
            selectedKeys={[location.pathname]}
            style={{
              fontSize: '16px',
              fontWeight: 500,
              padding: collapsed ? '0px 16px' : '0px 4px',
              borderRight: 0,
              marginTop: 8,
              backgroundColor: 'transparent',
            }}
            theme="light"
            items={sidebarMenuItems}
          />
        </div>

        {/* 用户信息区域 - 贴近屏幕底部 */}
        <div style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          borderTop: '1px solid #f0f0f0',
          padding: collapsed ? '12px 0' : '12px 16px',
          background: '#fff',
          zIndex: 10,
        }}>
          {!collapsed ? (
            <Dropdown menu={{ items: userMenuItems }} placement="topRight" trigger={['click']}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                padding: '8px',
                borderRadius: '6px',
                cursor: 'pointer',
                transition: 'background-color 0.2s',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = '#f5f5f5';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}>
                <Avatar
                  size={32}
                  style={{ backgroundColor: '#262626', marginRight: 12 }}
                  icon={<UserOutlined />}
                />
                <div style={{ flex: 1, minWidth: 0 }}>
                  <div style={{
                    fontSize: '14px',
                    fontWeight: 500,
                    color: '#262626',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }}>
                    {user?.name || user?.username}
                  </div>
                  <div style={{
                    fontSize: '12px',
                    color: '#8c8c8c',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }}>
                    {user?.username}
                  </div>
                </div>
              </div>
            </Dropdown>
          ) : (
            <div style={{ display: 'flex', justifyContent: 'center' }}>
              <Dropdown menu={{ items: userMenuItems }} placement="topRight" trigger={['click']}>
                <Avatar
                  size={40}
                  style={{ backgroundColor: '#000000', cursor: 'pointer' }}
                  icon={<UserOutlined />}
                />
              </Dropdown>
            </div>
          )}
        </div>
      </Sider>

      {/* 右侧内容区域 */}
      <Layout style={{
        // 移除marginLeft，让Layout自然分割空间
      }}>
        {/* 主内容区域 */}
        <Content style={{
          padding: '24px',
          background: '#f0f2f5',
          overflow: 'auto',
          minHeight: '100vh', // 确保内容区域至少占满屏幕高度
        }}>
          {children}
        </Content>
      </Layout>
    </Layout>
    </ConfigProvider>
  );
};

export default MainLayout;
